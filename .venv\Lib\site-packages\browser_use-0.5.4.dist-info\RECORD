../../Scripts/browser-use.exe,sha256=uACljV1bHAgoBPiemYHwn7Zk8BvBmRmQDGWd8HAG96c,108432
../../Scripts/browseruse.exe,sha256=uACljV1bHAgoBPiemYHwn7Zk8BvBmRmQDGWd8HAG96c,108432
browser_use-0.5.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
browser_use-0.5.4.dist-info/METADATA,sha256=aB5JNc-wDPS5AIXadsYJw30oc7HcASpUctGjxtZJbbI,12772
browser_use-0.5.4.dist-info/RECORD,,
browser_use-0.5.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
browser_use-0.5.4.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
browser_use-0.5.4.dist-info/entry_points.txt,sha256=NceUXLtKZs9AznxXL8P1rxVQVpF8jyk0x3SXZGhU1VE,87
browser_use-0.5.4.dist-info/licenses/LICENSE,sha256=E1xXZxsO6VdmmwWgygDMBvZFSW01Hi5zKDLG4nbaml4,1069
browser_use/__init__.py,sha256=ckr6TYBBryrpS-1CZme7EZDblE05Ft_pmymWp0fF8gY,1954
browser_use/__pycache__/__init__.cpython-312.pyc,,
browser_use/__pycache__/cli.cpython-312.pyc,,
browser_use/__pycache__/config.cpython-312.pyc,,
browser_use/__pycache__/exceptions.cpython-312.pyc,,
browser_use/__pycache__/logging_config.cpython-312.pyc,,
browser_use/__pycache__/observability.cpython-312.pyc,,
browser_use/__pycache__/utils.cpython-312.pyc,,
browser_use/agent/__pycache__/cloud_events.cpython-312.pyc,,
browser_use/agent/__pycache__/gif.cpython-312.pyc,,
browser_use/agent/__pycache__/prompts.cpython-312.pyc,,
browser_use/agent/__pycache__/service.cpython-312.pyc,,
browser_use/agent/__pycache__/views.cpython-312.pyc,,
browser_use/agent/cloud_events.py,sha256=-oQvWsKa5m-Ep7W79NRnOdpsM0UlHr8xTlGTbuD0Riw,10597
browser_use/agent/gif.py,sha256=Yj25gXjXvZ6jh0ykNPZjyosntmJeqleE71XAVHbU4Hc,10360
browser_use/agent/message_manager/__pycache__/service.cpython-312.pyc,,
browser_use/agent/message_manager/__pycache__/utils.cpython-312.pyc,,
browser_use/agent/message_manager/__pycache__/views.cpython-312.pyc,,
browser_use/agent/message_manager/service.py,sha256=S24_tO1551GKLDM8kjzFgY4zyd8kD0_aRMjamdjdt6A,14861
browser_use/agent/message_manager/utils.py,sha256=t0E71oIptfH8vCsMkm4pjTbbDMGZKqvhRBEhnGLqxDo,1372
browser_use/agent/message_manager/views.py,sha256=9C7m4GMlWVYDQAHN3_MLSsdhzSYvf5qeupc73jUQIRk,2497
browser_use/agent/prompts.py,sha256=BfE_1YBF-PT82Nw3eKJ9N3iWdWndQH_Ut565zI_TMKQ,13229
browser_use/agent/service.py,sha256=ljPdcsg2P_8yo5ftZse8h35drUvoZrxle5gxbkFSfg0,67291
browser_use/agent/system_prompt.md,sha256=aUJLdWSJBDT0VNNUxYN_ynt4XJJc9w4SanfSSJ3_BXw,14740
browser_use/agent/system_prompt_no_thinking.md,sha256=NL5FHMHx-Gv5vnIgXBH0fvaUP5b6qtOL7SS3gKpx1vQ,14565
browser_use/agent/views.py,sha256=SGB41megHaWTOP8o9wkRXyuteb_L_dfmjTGci-TKoWs,17036
browser_use/browser/__init__.py,sha256=I0EhXT22YZMx1Z4C9cFvxUWu088TjCBeDkcnMKW3Bzg,292
browser_use/browser/__pycache__/__init__.cpython-312.pyc,,
browser_use/browser/__pycache__/browser.cpython-312.pyc,,
browser_use/browser/__pycache__/context.cpython-312.pyc,,
browser_use/browser/__pycache__/extensions.cpython-312.pyc,,
browser_use/browser/__pycache__/profile.cpython-312.pyc,,
browser_use/browser/__pycache__/session.cpython-312.pyc,,
browser_use/browser/__pycache__/types.cpython-312.pyc,,
browser_use/browser/__pycache__/utils.cpython-312.pyc,,
browser_use/browser/__pycache__/views.cpython-312.pyc,,
browser_use/browser/browser.py,sha256=Gf9mbJdqnfWfNyCnq03jQheSikEsaOV6DoGLifhGQFY,269
browser_use/browser/context.py,sha256=HQUnwuVfXnWGGmc91JtZ3KTi2QKQsRDurfpnd-yB3k8,319
browser_use/browser/extensions.py,sha256=n5Z3hUrnvUcDTynaGR4fRiUDSM60Ex-cPSB0eBb6kaQ,15630
browser_use/browser/profile.py,sha256=dWLLOgeJm1Vzl5N8keflDJZvltz-2JUt1QbyZv-VzCE,33372
browser_use/browser/session.py,sha256=OTmbOyFYv3ehH3KDbrSIErt2yM6B9gyOZZmzET5D480,186376
browser_use/browser/types.py,sha256=tPONUDAL-QsY4QGV9IksZtug3CtEXMQlOjnWgmriAaQ,2963
browser_use/browser/utils.py,sha256=0LScFVR9uN-dhd0NbOxxnmZFR4fy_467aitKVzQZ7vs,1219
browser_use/browser/views.py,sha256=7UATL4_o7Lku5o1uxRwbO_EQqRDldagkEz0RvNQUYsY,2181
browser_use/cli.py,sha256=BrtpGxr3nG3lcmz6AhuinMEqvBxq7nX-g9ax_Z-rEwo,53797
browser_use/config.py,sha256=r0tm2dPbReKJsI1Kss5eMR-YAOT6cm-vkogdDFSxwqI,14798
browser_use/controller/__pycache__/service.cpython-312.pyc,,
browser_use/controller/__pycache__/views.cpython-312.pyc,,
browser_use/controller/registry/__pycache__/service.cpython-312.pyc,,
browser_use/controller/registry/__pycache__/views.cpython-312.pyc,,
browser_use/controller/registry/service.py,sha256=hvykStijkSdI08Suu1jmiKpKfNjfPfHUb6KHnNVY9Es,22818
browser_use/controller/registry/views.py,sha256=p7gYBr_8tHs9m-xHZeQkW1rMwXnjRixsRqFzEpRDsOY,6245
browser_use/controller/service.py,sha256=CytsmVydZzUYnLTwZ8mFbErGCfgWNyjJgtDrsafiV5I,47945
browser_use/controller/views.py,sha256=1I_I_L9F8OpwIBNCeO44c3PcHoC2LCKWdSfxA7dAEks,2810
browser_use/dom/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
browser_use/dom/__pycache__/__init__.cpython-312.pyc,,
browser_use/dom/__pycache__/service.cpython-312.pyc,,
browser_use/dom/__pycache__/utils.cpython-312.pyc,,
browser_use/dom/__pycache__/views.cpython-312.pyc,,
browser_use/dom/clickable_element_processor/__pycache__/service.cpython-312.pyc,,
browser_use/dom/clickable_element_processor/service.py,sha256=oFKPQw84HJCirAN_ic1lZrKBfwDvripRoAK7zWQ5eoE,2681
browser_use/dom/dom_tree/index.js,sha256=Z8Sp-CldRqaG_3URCHkhf3Y6AGGhe2aQgVf7Xh_S6IY,48777
browser_use/dom/history_tree_processor/__pycache__/service.cpython-312.pyc,,
browser_use/dom/history_tree_processor/__pycache__/view.cpython-312.pyc,,
browser_use/dom/history_tree_processor/service.py,sha256=LElri3-bsiVaXgiZpj3TzOGDgw_WfHWOaTw9_QWDOVA,4143
browser_use/dom/history_tree_processor/view.py,sha256=Ikje1lsWxSOYSpMPTgMUr_YMkLlhMnhjmxWvqr_MfLU,1674
browser_use/dom/playground/__pycache__/extraction.cpython-312.pyc,,
browser_use/dom/playground/__pycache__/process_dom.cpython-312.pyc,,
browser_use/dom/playground/__pycache__/test_accessibility.cpython-312.pyc,,
browser_use/dom/playground/extraction.py,sha256=TXa7QrY-TWheXnehdoeVEqoah02WQXE24jJ9R2fRU48,7092
browser_use/dom/playground/process_dom.py,sha256=0edKcc9BUgYxValr7Kv27mPUOnMSbs2wULbj5WNvkjw,1299
browser_use/dom/playground/test_accessibility.py,sha256=ryqNL-Xkyq3vDeMRAhmfe7NRu1aQgq2zFFHz_UkTcYc,3248
browser_use/dom/service.py,sha256=P6EogvjGECR3lwklAEomJXnADTkA6k4m0N13T4bUzxA,6802
browser_use/dom/utils.py,sha256=p8Vf4nP518a4o7i0E2NqHhuUQGcxRxiWbWPs-EDN-VQ,132
browser_use/dom/views.py,sha256=BJSK50lHWtOn4qhE2-3huenzpAr-Nbjf78llB1svHqo,8839
browser_use/exceptions.py,sha256=Bn79JaO4kwiJ7YKDbtiebrgaNvzHn6PgKmbaNf_XrpI,186
browser_use/filesystem/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
browser_use/filesystem/__pycache__/__init__.cpython-312.pyc,,
browser_use/filesystem/__pycache__/file_system.cpython-312.pyc,,
browser_use/filesystem/file_system.py,sha256=a2CT_uS2LaXsW-LbXPmk3JUqqq9ZmFevJ-30ZuUDDIU,14509
browser_use/integrations/gmail/__init__.py,sha256=HBMUrcVJJ903jEIvI5OHssLfpWCzHFOznlrUPb00QJA,1050
browser_use/integrations/gmail/__pycache__/__init__.cpython-312.pyc,,
browser_use/integrations/gmail/__pycache__/actions.cpython-312.pyc,,
browser_use/integrations/gmail/__pycache__/service.cpython-312.pyc,,
browser_use/integrations/gmail/actions.py,sha256=A54Szq4M3mjCKOt9OvyHZHaLIco7D1kYkU7upAukbRw,4227
browser_use/integrations/gmail/service.py,sha256=gfccVzBnZFRmPiIZ9MHwe7WvERUqV49EkWSj45fuJdE,7708
browser_use/llm/__init__.py,sha256=lMsL0OD80jzU2GAzo4LQWe2WzddI-0VtbBUr2AEeoNw,1494
browser_use/llm/__pycache__/__init__.cpython-312.pyc,,
browser_use/llm/__pycache__/base.cpython-312.pyc,,
browser_use/llm/__pycache__/exceptions.cpython-312.pyc,,
browser_use/llm/__pycache__/messages.cpython-312.pyc,,
browser_use/llm/__pycache__/schema.cpython-312.pyc,,
browser_use/llm/__pycache__/views.cpython-312.pyc,,
browser_use/llm/anthropic/__pycache__/chat.cpython-312.pyc,,
browser_use/llm/anthropic/__pycache__/serializer.cpython-312.pyc,,
browser_use/llm/anthropic/chat.py,sha256=gxnpWA5oLoaWIbZGHDXfSoz34fGB2EGw9rV2cO9PiX4,6785
browser_use/llm/anthropic/serializer.py,sha256=6fP1GTgFOkUHSmD0sxZs_HJ7AgtDGg6lc7JZ1PTnpBo,10338
browser_use/llm/aws/__init__.py,sha256=Swe8mYDUC8s9a9S4GpbmCi4OuZivWzoJ7rOEX-HJthc,187
browser_use/llm/aws/__pycache__/__init__.cpython-312.pyc,,
browser_use/llm/aws/__pycache__/chat_anthropic.cpython-312.pyc,,
browser_use/llm/aws/__pycache__/chat_bedrock.cpython-312.pyc,,
browser_use/llm/aws/__pycache__/serializer.cpython-312.pyc,,
browser_use/llm/aws/chat_anthropic.py,sha256=h0z316EYq-L93Ehl7jjeUHp-O5rhTIXLx6rxWVF4498,7947
browser_use/llm/aws/chat_bedrock.py,sha256=_VL4ku-Vef9D8zBW-sdHXYdlMaF2c0qEliXqJbM6FfU,8930
browser_use/llm/aws/serializer.py,sha256=HberFwAwuPFZ13to8eKg4oFJJ6uAUG70ElfZhXLRDqQ,7958
browser_use/llm/azure/__pycache__/chat.cpython-312.pyc,,
browser_use/llm/azure/chat.py,sha256=V83TsBJhuJQ8Ot8aBbxZtI7d-jkDew30ottMV2Srbsg,2731
browser_use/llm/base.py,sha256=a-0nR-ukFKdZNa7-rni3pUzwp8oro5Du-bFAlthgqlY,1457
browser_use/llm/exceptions.py,sha256=UDlMZwZMNxAedWkGk9PaRk6pw2NQ81ujV7i1SM69MwA,568
browser_use/llm/google/__init__.py,sha256=i7W91y9J4FxyqY_c25ZVtro5HcNg7peh1e6XTwsYlmg,77
browser_use/llm/google/__pycache__/__init__.cpython-312.pyc,,
browser_use/llm/google/__pycache__/chat.cpython-312.pyc,,
browser_use/llm/google/__pycache__/serializer.cpython-312.pyc,,
browser_use/llm/google/chat.py,sha256=ysP5iLyMQHhGISZYM12LbTkL_tMPT1srQLvw7NDqz_U,12098
browser_use/llm/google/serializer.py,sha256=EqRZh0l0DtJU3waAyWRw4bYiN2i6QwCbIMq3moeewuI,3045
browser_use/llm/groq/__pycache__/chat.cpython-312.pyc,,
browser_use/llm/groq/__pycache__/parser.cpython-312.pyc,,
browser_use/llm/groq/__pycache__/serializer.cpython-312.pyc,,
browser_use/llm/groq/chat.py,sha256=2fLjO5TL7o429j7vE47ZbUTv7JLU3NR16XLK8IuREf8,5412
browser_use/llm/groq/parser.py,sha256=MVmzXiC6WqKAXDkQoRjzBQ4bv01bUHLq9f_J2tgVpFA,4944
browser_use/llm/groq/serializer.py,sha256=ImWUCdpHB3recMrWt4t_3iANSvkcgkewUQKCRSNKrpo,5194
browser_use/llm/messages.py,sha256=3PyrtEcOIQVoWa9CGYiBolcsNGlXc-Dn3En_zVSCv2k,6575
browser_use/llm/ollama/__pycache__/chat.cpython-312.pyc,,
browser_use/llm/ollama/__pycache__/serializer.cpython-312.pyc,,
browser_use/llm/ollama/chat.py,sha256=jCH339nmBi48axDLfc-L1DVTJSjNzojrBA34sV38yWY,2516
browser_use/llm/ollama/serializer.py,sha256=BouWjM1qXUHS_hH07h09ZgS908Qb1ObckAvQLLV9kxU,4051
browser_use/llm/openai/__pycache__/chat.cpython-312.pyc,,
browser_use/llm/openai/__pycache__/like.cpython-312.pyc,,
browser_use/llm/openai/__pycache__/serializer.cpython-312.pyc,,
browser_use/llm/openai/chat.py,sha256=dM5hXQSna8ckoe4E5XYimOCoQGYLtf0hhFuiuUYt6iM,7124
browser_use/llm/openai/like.py,sha256=kxJN-CUzZR4hSpC-3LPk5-V3vOEfrWNImtIsaJTbdq8,291
browser_use/llm/openai/serializer.py,sha256=MgHqpdDNceGjsf2HJDIY21F4R6dcHSB1Uln7Lmz8n5Y,5866
browser_use/llm/openrouter/__pycache__/chat.cpython-312.pyc,,
browser_use/llm/openrouter/__pycache__/serializer.cpython-312.pyc,,
browser_use/llm/openrouter/chat.py,sha256=4MoAFqwaNKaYiWZc1kKfk8-TDToN6DrhskA4a2CHCL8,6215
browser_use/llm/openrouter/serializer.py,sha256=IYBtGrE7GUmUy7chcS6on_ekQx6FR95yJ648UEnUCDI,848
browser_use/llm/schema.py,sha256=-rJL8vReLAHZlYDgA9BbtKBXE-XBj9mkIJ_lJz1a7Dg,5864
browser_use/llm/views.py,sha256=Gzq05CLLJAGDbwB5lGC1h3-G2S1GNpA9ZCpmXh6Saro,1187
browser_use/logging_config.py,sha256=UUkN2yAk324xF6X0zwxw0o1ycJTXgRj0m6EDRdCI3B0,4674
browser_use/mcp/__init__.py,sha256=71SW3NSPrwfNieHFd-_xzpk93ZlhrZI-h-f6odsxRuY,363
browser_use/mcp/__main__.py,sha256=Se3BRfz9n7OuSYZijhBspY6rpRvmeM-GDy9WBBxDAFQ,206
browser_use/mcp/__pycache__/__init__.cpython-312.pyc,,
browser_use/mcp/__pycache__/__main__.cpython-312.pyc,,
browser_use/mcp/__pycache__/client.cpython-312.pyc,,
browser_use/mcp/__pycache__/controller.cpython-312.pyc,,
browser_use/mcp/__pycache__/server.cpython-312.pyc,,
browser_use/mcp/client.py,sha256=-3fA7x9ELP9NXaGfWvNmMCZ7wOTxdwx4-DJZTOa87wM,14855
browser_use/mcp/controller.py,sha256=WLydMKOavHjQDPHLpESyWskvzEry4q2yWc2Fb0Gc0l8,8392
browser_use/mcp/server.py,sha256=lvVf9KzZ-OOQtUSqaeBPu6xr1VV8i0O3z5CkNc1k88I,25730
browser_use/observability.py,sha256=7u1hBK95ZaUmq15bYithXGmKKKkDdRwiyEHRvbfdiwo,5776
browser_use/sync/__init__.py,sha256=7DlYMGJmejR475TJlmWkp5nitrh69iVjAP0gtTXRtns,221
browser_use/sync/__pycache__/__init__.cpython-312.pyc,,
browser_use/sync/__pycache__/auth.cpython-312.pyc,,
browser_use/sync/__pycache__/service.cpython-312.pyc,,
browser_use/sync/auth.py,sha256=8Rt00MY6KRgFLkG5-sRdZfyuqIjpoVEGdTXYmhmSEZ0,10424
browser_use/sync/service.py,sha256=nEHyfw--ghMEZTfV-k0w79yeAvz2eESikWdXzN00aiE,7429
browser_use/telemetry/__init__.py,sha256=fwuMWgIrfRP0g23f_z4jLldt73sQfBTAE_dCJ2kQOUY,369
browser_use/telemetry/__pycache__/__init__.cpython-312.pyc,,
browser_use/telemetry/__pycache__/service.cpython-312.pyc,,
browser_use/telemetry/__pycache__/views.cpython-312.pyc,,
browser_use/telemetry/service.py,sha256=z9XdVhM64fYdNQx3FiCeiq0NyY-Z8j3NDjQBurtvsyo,3062
browser_use/telemetry/views.py,sha256=zzzxXvLFTkvhWOQkYEWuruSnpd_0vU3EUFiEiyRYmcA,2047
browser_use/tokens/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
browser_use/tokens/__pycache__/__init__.cpython-312.pyc,,
browser_use/tokens/__pycache__/service.cpython-312.pyc,,
browser_use/tokens/__pycache__/views.cpython-312.pyc,,
browser_use/tokens/service.py,sha256=_6utbxjjsoPoJaTAWsSfajMl0vn5L5LB_fSB0P5Yx9o,19825
browser_use/tokens/views.py,sha256=B-9guL9fIZZKU-PkkMZlyTDOORNq_JO_1dsP_CCG0zk,2306
browser_use/utils.py,sha256=HcFuvaG8Pzx27hUXvOfBTt0-7q40NBCcNaaQCdIjwlU,20912
